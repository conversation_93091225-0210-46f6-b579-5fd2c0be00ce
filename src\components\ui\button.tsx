import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-rose-gold text-white shadow hover:bg-rose-gold-dark",
        destructive: "bg-red-500 text-white shadow-sm hover:bg-red-600",
        outline: "border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white",
        secondary: "bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark",
        ghost: "hover:bg-rose-gold-light hover:text-rose-gold-dark",
        link: "text-rose-gold-dark underline-offset-4 hover:underline",
        gradient: "bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        xl: "h-12 rounded-lg px-10 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
