'use client'

import { Phone, MessageCircle, MapPin } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'

export default function ContactHero() {
  return (
    <Section className="pt-24 pb-16 bg-gradient-to-br from-cream via-white to-blush-pink-light">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <Badge variant="secondary" className="text-sm px-4 py-2 mb-4">
            <Phone className="w-4 h-4 mr-2" />
            Contact & Booking
          </Badge>
          
          <h1 className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight">
            Let's Create Your
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Perfect Look
            </span>
          </h1>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto">
            Ready to book your makeup session? Get in touch with us today for consultations, 
            bookings, or any questions about our services. We're here to help you look and 
            feel absolutely stunning.
          </p>
        </AnimatedElement>

        {/* Quick Contact Options */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <Phone className="w-8 h-8 text-white" />
              </div>
              <div className="text-lg font-semibold text-text-primary mb-1">Call Us</div>
              <div className="text-text-secondary">+977-9800000000</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <div className="text-lg font-semibold text-text-primary mb-1">WhatsApp</div>
              <div className="text-text-secondary">Quick Response</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                <MapPin className="w-8 h-8 text-white" />
              </div>
              <div className="text-lg font-semibold text-text-primary mb-1">Visit Us</div>
              <div className="text-text-secondary">Biratnagar, Nepal</div>
            </div>
          </div>
        </AnimatedElement>

        {/* Response Time */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="flex flex-wrap justify-center gap-3 max-w-2xl mx-auto">
            {['Quick Response', 'Professional Service', 'Flexible Scheduling', 'Free Consultation'].map((feature) => (
              <Badge key={feature} variant="outline" className="text-sm px-4 py-2">
                {feature}
              </Badge>
            ))}
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
