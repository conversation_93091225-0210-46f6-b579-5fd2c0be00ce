/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://anjalimakeup.com',
  generateRobotsTxt: false, // We have a custom robots.txt
  generateIndexSitemap: false,
  exclude: ['/admin/*', '/api/*'],
  additionalPaths: async (config) => {
    const result = []

    // Add blog posts
    const blogPosts = [
      'bridal-makeup-trends-2024',
      'choosing-right-makeup-skin-tone',
      'traditional-nepali-makeup-guide',
      'makeup-longevity-tips',
      'seasonal-makeup-trends'
    ]

    blogPosts.forEach((slug) => {
      result.push({
        loc: `/blog/${slug}`,
        changefreq: 'weekly',
        priority: 0.7,
        lastmod: new Date().toISOString(),
      })
    })

    return result
  },
  transform: async (config, path) => {
    // Custom priority and changefreq for different pages
    const customConfig = {
      '/': { priority: 1.0, changefreq: 'daily' },
      '/about': { priority: 0.9, changefreq: 'monthly' },
      '/services': { priority: 0.9, changefreq: 'weekly' },
      '/packages': { priority: 0.8, changefreq: 'weekly' },
      '/portfolio': { priority: 0.8, changefreq: 'weekly' },
      '/blog': { priority: 0.7, changefreq: 'weekly' },
      '/contact': { priority: 0.6, changefreq: 'monthly' },
    }

    const pageConfig = customConfig[path] || { priority: 0.5, changefreq: 'monthly' }

    return {
      loc: path,
      changefreq: pageConfig.changefreq,
      priority: pageConfig.priority,
      lastmod: new Date().toISOString(),
    }
  },
}
