{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/app/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport AboutHero from '@/components/sections/about-hero'\nimport AboutStory from '@/components/sections/about-story'\nimport AboutExperience from '@/components/sections/about-experience'\nimport AboutCities from '@/components/sections/about-cities'\nimport AboutCTA from '@/components/sections/about-cta'\nimport { getSiteConfig } from '@/lib/data'\n\nconst siteConfig = getSiteConfig()\n\nexport const metadata: Metadata = {\n  title: `About ${siteConfig.site.name} | Professional Makeup Artist in Biratnagar`,\n  description: 'Learn about our professional makeup artist, her journey, expertise, and passion for enhancing natural beauty across Nepal.',\n  openGraph: {\n    title: `About ${siteConfig.site.name} | Professional Makeup Artist`,\n    description: 'Learn about our professional makeup artist, her journey, expertise, and passion for enhancing natural beauty across Nepal.',\n    url: `${siteConfig.site.url}/about`,\n  },\n}\n\nexport default function AboutPage() {\n  return (\n    <>\n      <AboutHero />\n      <AboutStory />\n      <AboutExperience />\n      <AboutCities />\n      <AboutCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAAA;;;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,2CAA2C,CAAC;IACjF,aAAa;IACb,WAAW;QACT,OAAO,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC;QACnE,aAAa;QACb,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IACrC;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;;;AAGP", "debugId": null}}]}