'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Eye, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { getGalleryItems, getGalleryCategories } from '@/lib/data'

export default function PortfolioGallery() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const galleryItems = getGalleryItems()
  const categories = getGalleryCategories()

  const filteredItems = selectedCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory)

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Work"
          title="Portfolio Gallery"
          description="Browse through our collection of stunning makeup transformations. Each image tells a story of beauty, confidence, and artistry."
        />
      </AnimatedElement>

      {/* Category Filter */}
      <AnimatedElement animation="slideUp" delay={0.3} className="mb-12">
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="group"
            >
              <Filter className="w-4 h-4 mr-2" />
              {category.name}
              <Badge variant="secondary" className="ml-2 text-xs">
                {category.count}
              </Badge>
            </Button>
          ))}
        </div>
      </AnimatedElement>

      {/* Gallery Grid */}
      <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredItems.map((item, index) => (
          <StaggeredItem key={item.id}>
            <div className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer">
              <Image
                src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80`}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <Badge variant="secondary" className="mb-3 text-xs">
                    {item.category}
                  </Badge>
                  <h3 className="font-display text-lg font-semibold mb-2">
                    {item.title}
                  </h3>
                  <p className="text-sm text-white/80 mb-3">
                    {item.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs border-white/30 text-white">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* View Icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Eye className="w-5 h-5 text-white" />
              </div>

              {/* Featured Badge */}
              {item.featured && (
                <div className="absolute top-4 left-4">
                  <Badge variant="default" className="text-xs">
                    Featured
                  </Badge>
                </div>
              )}
            </div>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Masonry Layout for larger screens */}
      <div className="hidden 2xl:block mt-16">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            title="Featured Showcase"
            description="A curated selection of our most stunning transformations in a beautiful masonry layout."
          />
        </AnimatedElement>

        <StaggeredContainer className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
          {galleryItems.filter(item => item.featured).map((item, index) => {
            const heights = ['aspect-[3/4]', 'aspect-square', 'aspect-[4/5]', 'aspect-[3/5]']
            const randomHeight = heights[index % heights.length]
            
            return (
              <StaggeredItem key={`masonry-${item.id}`}>
                <div className={`group relative ${randomHeight} overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer break-inside-avoid`}>
                  <Image
                    src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=500&fit=crop&crop=face&q=80`}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {item.category}
                      </Badge>
                      <h3 className="font-display text-base font-semibold mb-1">
                        {item.title}
                      </h3>
                      <p className="text-xs text-white/80">
                        {item.description}
                      </p>
                    </div>
                  </div>

                  {/* View Icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                </div>
              </StaggeredItem>
            )
          })}
        </StaggeredContainer>
      </div>

      {/* Load More Button */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-12 text-center">
        <p className="text-text-secondary mb-6">
          Showing {filteredItems.length} of {galleryItems.length} images
        </p>
        <Button variant="outline" size="lg" disabled>
          <Eye className="w-5 h-5 mr-2" />
          Load More Coming Soon
        </Button>
      </AnimatedElement>
    </Section>
  )
}
