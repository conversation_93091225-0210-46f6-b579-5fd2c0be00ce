{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/services-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/services-hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/services-hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/services-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/services-hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/services-hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/app/services/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport ServicesHero from '@/components/sections/services-hero'\nimport ServicesGrid from '@/components/sections/services-grid'\nimport ServicesCTA from '@/components/sections/services-cta'\nimport { getSiteConfig } from '@/lib/data'\n\nconst siteConfig = getSiteConfig()\n\nexport const metadata: Metadata = {\n  title: `Professional Makeup Services | ${siteConfig.site.name}`,\n  description: 'Comprehensive makeup services including bridal, party, traditional, and photoshoot makeup. Professional artistry for all your special occasions in Nepal.',\n  openGraph: {\n    title: `Professional Makeup Services | ${siteConfig.site.name}`,\n    description: 'Comprehensive makeup services including bridal, party, traditional, and photoshoot makeup. Professional artistry for all your special occasions in Nepal.',\n    url: `${siteConfig.site.url}/services`,\n  },\n}\n\nexport default function ServicesPage() {\n  return (\n    <>\n      <ServicesHero />\n      <ServicesGrid />\n      <ServicesCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;;;;;;;;;AAGA;AAAA;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,CAAC,+BAA+B,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;IAC/D,aAAa;IACb,WAAW;QACT,OAAO,CAAC,+BAA+B,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;QAC/D,aAAa;QACb,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;IACxC;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,kJAAA,CAAA,UAAY;;;;;0BACb,8OAAC;;;;;0BACD,8OAAC;;;;;;;AAGP", "debugId": null}}]}