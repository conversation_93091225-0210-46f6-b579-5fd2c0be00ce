{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: string): string {\n  return price.replace(/NPR\\s*/g, 'NPR ')\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function generateWhatsAppLink(phone: string, message: string): string {\n  const cleanPhone = phone.replace(/[^\\d+]/g, '')\n  const encodedMessage = encodeURIComponent(message)\n  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n\nexport function getImageUrl(imagePath: string): string {\n  // Handle placeholder images for development\n  if (imagePath.startsWith('/images/')) {\n    return `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=600&fit=crop&crop=face`\n  }\n  return imagePath\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^(\\+977)?[0-9]{10}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function scrollToElement(elementId: string, offset: number = 80): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top\n    const offsetPosition = elementPosition + window.pageYOffset - offset\n    \n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    })\n  }\n}\n\nexport function isInViewport(element: Element): boolean {\n  const rect = element.getBoundingClientRect()\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  )\n}\n\nexport function getReadingTime(content: string): string {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  const minutes = Math.ceil(words / wordsPerMinute)\n  return `${minutes} min read`\n}\n\nexport function generateSEOSchema(type: 'Organization' | 'LocalBusiness' | 'Article', data: any) {\n  const baseSchema = {\n    '@context': 'https://schema.org',\n    '@type': type\n  }\n\n  switch (type) {\n    case 'LocalBusiness':\n      return {\n        ...baseSchema,\n        name: data.name,\n        description: data.description,\n        url: data.url,\n        telephone: data.phone,\n        address: {\n          '@type': 'PostalAddress',\n          streetAddress: data.address.street,\n          addressLocality: data.address.city,\n          addressRegion: data.address.state,\n          addressCountry: data.address.country,\n          postalCode: data.address.zipCode\n        },\n        geo: data.geo,\n        openingHours: data.openingHours,\n        priceRange: data.priceRange,\n        serviceArea: data.serviceArea\n      }\n    \n    case 'Article':\n      return {\n        ...baseSchema,\n        headline: data.title,\n        description: data.description,\n        author: {\n          '@type': 'Person',\n          name: data.author\n        },\n        datePublished: data.publishedAt,\n        dateModified: data.updatedAt,\n        image: data.image,\n        url: data.url\n      }\n    \n    default:\n      return baseSchema\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,qBAAqB,KAAa,EAAE,OAAe;IACjE,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW;IAC5C,MAAM,iBAAiB,mBAAmB;IAC1C,OAAO,CAAC,cAAc,EAAE,WAAW,MAAM,EAAE,gBAAgB;AAC7D;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAEO,SAAS,YAAY,SAAiB;IAC3C,4CAA4C;IAC5C,IAAI,UAAU,UAAU,CAAC,aAAa;QACpC,OAAO,CAAC,2FAA2F,CAAC;IACtG;IACA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,EAAE;IACpE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAEO,SAAS,aAAa,OAAgB;IAC3C,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ;IAClC,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9B;AAEO,SAAS,kBAAkB,IAAkD,EAAE,IAAS;IAC7F,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,KAAK,KAAK,GAAG;gBACb,WAAW,KAAK,KAAK;gBACrB,SAAS;oBACP,SAAS;oBACT,eAAe,KAAK,OAAO,CAAC,MAAM;oBAClC,iBAAiB,KAAK,OAAO,CAAC,IAAI;oBAClC,eAAe,KAAK,OAAO,CAAC,KAAK;oBACjC,gBAAgB,KAAK,OAAO,CAAC,OAAO;oBACpC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAClC;gBACA,KAAK,KAAK,GAAG;gBACb,cAAc,KAAK,YAAY;gBAC/B,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,QAAQ;oBACN,SAAS;oBACT,MAAM,KAAK,MAAM;gBACnB;gBACA,eAAe,KAAK,WAAW;gBAC/B,cAAc,KAAK,SAAS;gBAC5B,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG;YACf;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-rose-gold text-white shadow hover:bg-rose-gold-dark\",\n        destructive: \"bg-red-500 text-white shadow-sm hover:bg-red-600\",\n        outline: \"border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white\",\n        secondary: \"bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark\",\n        ghost: \"hover:bg-rose-gold-light hover:text-rose-gold-dark\",\n        link: \"text-rose-gold-dark underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/lib/data.ts"], "sourcesContent": ["import servicesData from '@/data/services.json'\nimport packagesData from '@/data/packages.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport galleryData from '@/data/gallery.json'\nimport blogData from '@/data/blog.json'\nimport siteConfigData from '@/data/site-config.json'\nimport { formatPrice, formatDate, generateWhatsAppLink } from '@/lib/utils'\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  duration: string\n  price: string\n  image: string\n  category: string\n  popular: boolean\n}\n\nexport interface Package {\n  id: string\n  title: string\n  description: string\n  services: string[]\n  duration: string\n  originalPrice: string\n  discountedPrice: string\n  savings: string\n  image: string\n  popular: boolean\n  badge: string | null\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  location: string\n  service: string\n  rating: number\n  text: string\n  image: string\n  date: string\n  featured: boolean\n}\n\nexport interface GalleryItem {\n  id: string\n  title: string\n  description: string\n  image: string\n  category: string\n  featured: boolean\n  tags: string[]\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  author: string\n  publishedAt: string\n  updatedAt: string\n  featured: boolean\n  image: string\n  tags: string[]\n  category: string\n  readTime: string\n  seo: {\n    metaTitle: string\n    metaDescription: string\n    keywords: string[]\n  }\n}\n\nexport interface SiteConfig {\n  site: {\n    name: string\n    tagline: string\n    description: string\n    url: string\n    logo: string\n    favicon: string\n  }\n  contact: {\n    phone: string\n    whatsapp: string\n    email: string\n    address: {\n      street: string\n      city: string\n      state: string\n      country: string\n      zipCode: string\n    }\n    workingHours: Record<string, string>\n  }\n  social: {\n    facebook: string\n    instagram: string\n    tiktok: string\n    youtube: string\n  }\n  serviceAreas: Array<{\n    name: string\n    primary: boolean\n    travelFee: number\n  }>\n  whatsappMessage: string\n  seo: {\n    defaultTitle: string\n    defaultDescription: string\n    keywords: string[]\n    author: string\n    twitterHandle: string\n  }\n  analytics: {\n    googleAnalyticsId: string\n  }\n}\n\n// Services\nexport function getServices(): Service[] {\n  return servicesData.services\n}\n\nexport function getService(id: string): Service | undefined {\n  return servicesData.services.find(service => service.id === id)\n}\n\nexport function getPopularServices(): Service[] {\n  return servicesData.services.filter(service => service.popular)\n}\n\nexport function getServicesByCategory(category: string): Service[] {\n  return servicesData.services.filter(service => service.category === category)\n}\n\n// Packages\nexport function getPackages(): Package[] {\n  return packagesData.packages\n}\n\nexport function getPackage(id: string): Package | undefined {\n  return packagesData.packages.find(pkg => pkg.id === id)\n}\n\nexport function getPopularPackages(): Package[] {\n  return packagesData.packages.filter(pkg => pkg.popular)\n}\n\n// Testimonials\nexport function getTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials\n}\n\nexport function getFeaturedTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => testimonial.featured)\n}\n\nexport function getTestimonialsByService(service: string): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => \n    testimonial.service.toLowerCase().includes(service.toLowerCase())\n  )\n}\n\n// Gallery\nexport function getGalleryItems(): GalleryItem[] {\n  return galleryData.gallery\n}\n\nexport function getFeaturedGalleryItems(): GalleryItem[] {\n  return galleryData.gallery.filter(item => item.featured)\n}\n\nexport function getGalleryItemsByCategory(category: string): GalleryItem[] {\n  if (category === 'all') return galleryData.gallery\n  return galleryData.gallery.filter(item => item.category === category)\n}\n\nexport function getGalleryCategories() {\n  return galleryData.categories\n}\n\n// Blog\nexport function getBlogPosts(): BlogPost[] {\n  return blogData.posts\n}\n\nexport function getBlogPost(slug: string): BlogPost | undefined {\n  return blogData.posts.find(post => post.slug === slug)\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return blogData.posts.filter(post => post.featured)\n}\n\nexport function getBlogPostsByCategory(category: string): BlogPost[] {\n  return blogData.posts.filter(post => \n    post.category.toLowerCase().replace(/\\s+/g, '-') === category.toLowerCase()\n  )\n}\n\nexport function getBlogCategories() {\n  return blogData.categories\n}\n\nexport function getRelatedBlogPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {\n  return blogData.posts\n    .filter(post => \n      post.id !== currentPost.id && \n      (post.category === currentPost.category || \n       post.tags.some(tag => currentPost.tags.includes(tag)))\n    )\n    .slice(0, limit)\n}\n\n// Site Configuration\nexport function getSiteConfig(): SiteConfig {\n  return siteConfigData as SiteConfig\n}\n\nexport function getContactInfo() {\n  return siteConfigData.contact\n}\n\nexport function getSocialLinks() {\n  return siteConfigData.social\n}\n\nexport function getServiceAreas() {\n  return siteConfigData.serviceAreas\n}\n\nexport function getWhatsAppMessage() {\n  return siteConfigData.whatsappMessage\n}\n\nexport function getSEODefaults() {\n  return siteConfigData.seo\n}\n\n// Re-export utility functions\nexport { formatPrice, formatDate, generateWhatsAppLink }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAsHO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO;AAChE;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACtE;AAGO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AACtD;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO;AACxD;AAGO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY;AACtC;AAEO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ;AACjF;AAEO,SAAS,yBAAyB,OAAe;IACtD,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAC1C,YAAY,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;AAElE;AAGO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;AAC5B;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACzD;AAEO,SAAS,0BAA0B,QAAgB;IACxD,IAAI,aAAa,OAAO,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;IAClD,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,UAAU;AAC/B;AAGO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK;AACvB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AACnD;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACpD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAC3B,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,SAAS,SAAS,WAAW;AAE7E;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,UAAU;AAC5B;AAEO,SAAS,oBAAoB,WAAqB,EAAE,QAAgB,CAAC;IAC1E,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAClB,MAAM,CAAC,CAAA,OACN,KAAK,EAAE,KAAK,YAAY,EAAE,IAC1B,CAAC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,GAEvD,KAAK,CAAC,GAAG;AACd;AAGO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc;AACvB;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,OAAO;AAC/B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,MAAM;AAC9B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,YAAY;AACpC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,eAAe;AACvC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X, Phone, Instagram, Facebook } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getSiteConfig, getSocialLinks } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Packages', href: '/packages' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport default function Header() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n  const siteConfig = getSiteConfig()\n  const socialLinks = getSocialLinks()\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10)\n    }\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-md'\n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center\">\n                <span className=\"text-white font-display font-bold text-lg\">A</span>\n              </div>\n              <span className=\"font-display text-xl font-semibold text-text-primary\">\n                {siteConfig.site.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-text-primary hover:text-rose-gold-dark px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Link\n                href={socialLinks.instagram}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n              >\n                <Instagram className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.facebook}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n              >\n                <Facebook className=\"h-5 w-5\" />\n              </Link>\n            </div>\n            <Button asChild variant=\"gradient\" size=\"sm\">\n              <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                <Phone className=\"h-4 w-4\" />\n                Book Now\n              </Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsOpen(!isOpen)}\n              aria-label=\"Toggle menu\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white border-t border-gray-200\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-text-primary hover:text-rose-gold-dark block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 pb-2 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between px-3\">\n                  <div className=\"flex items-center space-x-4\">\n                    <Link\n                      href={socialLinks.instagram}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                    >\n                      <Instagram className=\"h-6 w-6\" />\n                    </Link>\n                    <Link\n                      href={socialLinks.facebook}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                    >\n                      <Facebook className=\"h-6 w-6\" />\n                    </Link>\n                  </div>\n                  <Button asChild variant=\"gradient\" size=\"sm\">\n                    <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                      <Phone className=\"h-4 w-4\" />\n                      Book Now\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAYA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,2CACA;;0BAGN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,8OAAC;wCAAK,WAAU;kDACb,WAAW,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAM3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,SAAS;4CAC3B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,QAAQ;4CAC1B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAW,MAAK;8CACtC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM;wCAAc,QAAO;wCAAS,KAAI;;0DAC5C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;sCAOnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,cAAW;0CAEV,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,YAAY,SAAS;oDAC3B,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,YAAY,QAAQ;oDAC1B,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGxB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAW,MAAK;sDACtC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM;gDAAc,QAAO;gDAAS,KAAI;;kEAC5C,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD", "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/seo/analytics.tsx"], "sourcesContent": ["'use client'\n\nimport Script from 'next/script'\nimport { getSiteConfig } from '@/lib/data'\n\nexport default function Analytics() {\n  const siteConfig = getSiteConfig()\n  const gaId = siteConfig.analytics.googleAnalyticsId\n\n  // Only load analytics in production\n  if (process.env.NODE_ENV !== 'production' || !gaId || gaId === 'G-XXXXXXXXXX') {\n    return null\n  }\n\n  return (\n    <>\n      <Script\n        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}\n        strategy=\"afterInteractive\"\n      />\n      <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n        {`\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${gaId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        `}\n      </Script>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,OAAO,WAAW,SAAS,CAAC,iBAAiB;IAEnD,oCAAoC;IACpC,wCAA+E;QAC7E,OAAO;IACT;;;AAqBF", "debugId": null}}]}