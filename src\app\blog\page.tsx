import { Metadata } from 'next'
import BlogHero from '@/components/sections/blog-hero'
import BlogGrid from '@/components/sections/blog-grid'
import BlogCTA from '@/components/sections/blog-cta'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `Beauty Blog & Tips | ${siteConfig.site.name}`,
  description: 'Expert makeup tips, beauty trends, and professional advice from our experienced makeup artist. Learn techniques and stay updated with the latest beauty trends.',
  openGraph: {
    title: `Beauty Blog & Tips | ${siteConfig.site.name}`,
    description: 'Expert makeup tips, beauty trends, and professional advice from our experienced makeup artist. Learn techniques and stay updated with the latest beauty trends.',
    url: `${siteConfig.site.url}/blog`,
  },
}

export default function BlogPage() {
  return (
    <>
      <BlogHero />
      <BlogGrid />
      <BlogCTA />
    </>
  )
}
