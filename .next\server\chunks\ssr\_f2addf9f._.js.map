{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark\",\n        secondary: \"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark\",\n        destructive: \"border-transparent bg-red-500 text-white shadow hover:bg-red-600\",\n        outline: \"text-text-primary border-gray-300\",\n        success: \"border-transparent bg-green-500 text-white shadow hover:bg-green-600\",\n        warning: \"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600\",\n        lavender: \"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/ui/section.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface SectionProps {\n  children: React.ReactNode\n  className?: string\n  id?: string\n  background?: 'default' | 'cream' | 'soft-gray' | 'gradient'\n}\n\nexport function Section({ \n  children, \n  className, \n  id, \n  background = 'default' \n}: SectionProps) {\n  const backgroundClasses = {\n    default: 'bg-white',\n    cream: 'bg-cream',\n    'soft-gray': 'bg-soft-gray',\n    gradient: 'bg-gradient-to-br from-cream to-soft-gray'\n  }\n\n  return (\n    <section \n      id={id}\n      className={cn(\n        'py-16 md:py-24',\n        backgroundClasses[background],\n        className\n      )}\n    >\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {children}\n      </div>\n    </section>\n  )\n}\n\ninterface SectionHeaderProps {\n  title: string\n  subtitle?: string\n  description?: string\n  centered?: boolean\n  className?: string\n}\n\nexport function SectionHeader({ \n  title, \n  subtitle, \n  description, \n  centered = true,\n  className \n}: SectionHeaderProps) {\n  return (\n    <div className={cn(\n      'mb-12 md:mb-16',\n      centered && 'text-center',\n      className\n    )}>\n      {subtitle && (\n        <p className=\"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2\">\n          {subtitle}\n        </p>\n      )}\n      <h2 className=\"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4\">\n        {title}\n      </h2>\n      {description && (\n        <p className=\"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed\">\n          {description}\n        </p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AASO,SAAS,QAAQ,EACtB,QAAQ,EACR,SAAS,EACT,EAAE,EACF,aAAa,SAAS,EACT;IACb,MAAM,oBAAoB;QACxB,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kBACA,iBAAiB,CAAC,WAAW,EAC7B;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAUO,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,IAAI,EACf,SAAS,EACU;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kBACA,YAAY,eACZ;;YAEC,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/ui/animated-element.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface AnimatedElementProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'bounce'\n  delay?: number\n  duration?: number\n  once?: boolean\n}\n\nconst animations = {\n  fadeIn: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n  },\n  slideUp: {\n    initial: { opacity: 0, y: 50 },\n    animate: { opacity: 1, y: 0 },\n  },\n  slideLeft: {\n    initial: { opacity: 0, x: 50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  slideRight: {\n    initial: { opacity: 0, x: -50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  scale: {\n    initial: { opacity: 0, scale: 0.8 },\n    animate: { opacity: 1, scale: 1 },\n  },\n  bounce: {\n    initial: { opacity: 0, y: -20 },\n    animate: { opacity: 1, y: 0 },\n  },\n}\n\nexport function AnimatedElement({\n  children,\n  className,\n  animation = 'fadeIn',\n  delay = 0,\n  duration = 0.6,\n  once = true,\n}: AnimatedElementProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      initial={selectedAnimation.initial}\n      whileInView={selectedAnimation.animate}\n      viewport={{ once, margin: '-100px' }}\n      transition={{\n        duration,\n        delay,\n        ease: 'easeOut',\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredContainerProps {\n  children: React.ReactNode\n  className?: string\n  staggerDelay?: number\n}\n\nexport function StaggeredContainer({\n  children,\n  className,\n  staggerDelay = 0.1,\n}: StaggeredContainerProps) {\n  return (\n    <motion.div\n      className={cn(className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-100px' }}\n      variants={{\n        hidden: {},\n        visible: {\n          transition: {\n            staggerChildren: staggerDelay,\n          },\n        },\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredItemProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale'\n}\n\nexport function StaggeredItem({\n  children,\n  className,\n  animation = 'slideUp',\n}: StaggeredItemProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      variants={{\n        hidden: selectedAnimation.initial,\n        visible: selectedAnimation.animate,\n      }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,aAAa;IACjB,QAAQ;QACN,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;IACxB;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,YAAY;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,OAAO;QACL,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;IAClC;IACA,QAAQ;QACN,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;AACF;AAEO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,YAAY,QAAQ,EACpB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,OAAO,IAAI,EACU;IACrB,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS,kBAAkB,OAAO;QAClC,aAAa,kBAAkB,OAAO;QACtC,UAAU;YAAE;YAAM,QAAQ;QAAS;QACnC,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAQO,SAAS,mBAAmB,EACjC,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EACM;IACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU;YACR,QAAQ,CAAC;YACT,SAAS;gBACP,YAAY;oBACV,iBAAiB;gBACnB;YACF;QACF;kBAEC;;;;;;AAGP;AAQO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,YAAY,SAAS,EACF;IACnB,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,UAAU;YACR,QAAQ,kBAAkB,OAAO;YACjC,SAAS,kBAAkB,OAAO;QACpC;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE5C;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/sections/services-hero.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\nimport { Section } from '@/components/ui/section'\nimport { AnimatedElement } from '@/components/ui/animated-element'\n\nexport default function ServicesHero() {\n  return (\n    <Section className=\"pt-24 pb-16 bg-gradient-to-br from-cream via-white to-blush-pink-light\">\n      <div className=\"text-center space-y-8\">\n        <AnimatedElement animation=\"slideUp\">\n          <Badge variant=\"secondary\" className=\"text-sm px-4 py-2 mb-4\">\n            <Palette className=\"w-4 h-4 mr-2\" />\n            Professional Makeup Services\n          </Badge>\n          \n          <h1 className=\"font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight\">\n            Transform Your Look with\n            <span className=\"block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text\">\n              Expert Artistry\n            </span>\n          </h1>\n          \n          <p className=\"text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto\">\n            From bridal elegance to party glamour, discover our comprehensive range of \n            professional makeup services designed to enhance your natural beauty for \n            every special occasion.\n          </p>\n        </AnimatedElement>\n\n        {/* Service Stats */}\n        <AnimatedElement animation=\"slideUp\" delay={0.3}>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3\">\n                <Palette className=\"w-8 h-8 text-white\" />\n              </div>\n              <div className=\"text-2xl font-bold text-text-primary mb-1\">6+</div>\n              <div className=\"text-text-secondary\">Service Types</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3\">\n                <Star className=\"w-8 h-8 text-white\" />\n              </div>\n              <div className=\"text-2xl font-bold text-text-primary mb-1\">5.0</div>\n              <div className=\"text-text-secondary\">Average Rating</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3\">\n                <Users className=\"w-8 h-8 text-white\" />\n              </div>\n              <div className=\"text-2xl font-bold text-text-primary mb-1\">100+</div>\n              <div className=\"text-text-secondary\">Happy Clients</div>\n            </div>\n          </div>\n        </AnimatedElement>\n\n        {/* Service Categories */}\n        <AnimatedElement animation=\"slideUp\" delay={0.5}>\n          <div className=\"flex flex-wrap justify-center gap-3 max-w-2xl mx-auto\">\n            {['Bridal', 'Party', 'Traditional', 'Photoshoot', 'Engagement', 'Lessons'].map((category) => (\n              <Badge key={category} variant=\"outline\" className=\"text-sm px-4 py-2\">\n                {category}\n              </Badge>\n            ))}\n          </div>\n        </AnimatedElement>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,+IAAA,CAAA,kBAAe;oBAAC,WAAU;;sCACzB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;;8CACnC,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAItC,8OAAC;4BAAG,WAAU;;gCAA0F;8CAEtG,8OAAC;oCAAK,WAAU;8CAAoF;;;;;;;;;;;;sCAKtG,8OAAC;4BAAE,WAAU;sCAAgE;;;;;;;;;;;;8BAQ/E,8OAAC,+IAA<PERSON>,CAAA,kBAAe;oBAAC,WAAU;oBAAU,OAAO;8BAC1C,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;8BAM3C,8OAAC,+IAAA,CAAA,kBAAe;oBAAC,WAAU;oBAAU,OAAO;8BAC1C,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAU;4BAAS;4BAAe;4BAAc;4BAAc;yBAAU,CAAC,GAAG,CAAC,CAAC,yBAC9E,8OAAC,iIAAA,CAAA,QAAK;gCAAgB,SAAQ;gCAAU,WAAU;0CAC/C;+BADS;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/palette.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/node_modules/lucide-react/src/icons/palette.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z',\n      key: 'e79jfc',\n    },\n  ],\n  ['circle', { cx: '13.5', cy: '6.5', r: '.5', fill: 'currentColor', key: '1okk4w' }],\n  ['circle', { cx: '17.5', cy: '10.5', r: '.5', fill: 'currentColor', key: 'f64h9f' }],\n  ['circle', { cx: '6.5', cy: '12.5', r: '.5', fill: 'currentColor', key: 'qy21gx' }],\n  ['circle', { cx: '8.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'fotxhn' }],\n];\n\n/**\n * @component @name Palette\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJhMSAxIDAgMCAxIDAtMjAgMTAgOSAwIDAgMSAxMCA5IDUgNSAwIDAgMS01IDVoLTIuMjVhMS43NSAxLjc1IDAgMCAwLTEuNCAyLjhsLjMuNGExLjc1IDEuNzUgMCAwIDEtMS40IDIuOHoiIC8+CiAgPGNpcmNsZSBjeD0iMTMuNSIgY3k9IjYuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTAuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxMi41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgogIDxjaXJjbGUgY3g9IjguNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/palette\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Palette = createLucideIcon('palette', __iconNode);\n\nexport default Palette;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,QAAA,CAAU;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAA,CAAA,CAAA,CAAG,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,cAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/star.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/users.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}