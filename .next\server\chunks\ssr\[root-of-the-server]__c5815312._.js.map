{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_c4ae686b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"playfair_display_c4ae686b-module__xLrk2G__className\",\n  \"variable\": \"playfair_display_c4ae686b-module__xLrk2G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_c4ae686b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Playfair_Display%22,%22arguments%22:[{%22variable%22:%22--font-display%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22playfairDisplay%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Playfair Display', 'Playfair Display Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_80f34f63.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_80f34f63-module__xyMXYq__className\",\n  \"variable\": \"poppins_80f34f63-module__xyMXYq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_80f34f63.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22variable%22:%22--font-sans%22,%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: string): string {\n  return price.replace(/NPR\\s*/g, 'NPR ')\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function generateWhatsAppLink(phone: string, message: string): string {\n  const cleanPhone = phone.replace(/[^\\d+]/g, '')\n  const encodedMessage = encodeURIComponent(message)\n  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n\nexport function getImageUrl(imagePath: string): string {\n  // Handle placeholder images for development\n  if (imagePath.startsWith('/images/')) {\n    return `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=600&fit=crop&crop=face`\n  }\n  return imagePath\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^(\\+977)?[0-9]{10}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function scrollToElement(elementId: string, offset: number = 80): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top\n    const offsetPosition = elementPosition + window.pageYOffset - offset\n    \n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    })\n  }\n}\n\nexport function isInViewport(element: Element): boolean {\n  const rect = element.getBoundingClientRect()\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  )\n}\n\nexport function getReadingTime(content: string): string {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  const minutes = Math.ceil(words / wordsPerMinute)\n  return `${minutes} min read`\n}\n\nexport function generateSEOSchema(type: 'Organization' | 'LocalBusiness' | 'Article', data: any) {\n  const baseSchema = {\n    '@context': 'https://schema.org',\n    '@type': type\n  }\n\n  switch (type) {\n    case 'LocalBusiness':\n      return {\n        ...baseSchema,\n        name: data.name,\n        description: data.description,\n        url: data.url,\n        telephone: data.phone,\n        address: {\n          '@type': 'PostalAddress',\n          streetAddress: data.address.street,\n          addressLocality: data.address.city,\n          addressRegion: data.address.state,\n          addressCountry: data.address.country,\n          postalCode: data.address.zipCode\n        },\n        geo: data.geo,\n        openingHours: data.openingHours,\n        priceRange: data.priceRange,\n        serviceArea: data.serviceArea\n      }\n    \n    case 'Article':\n      return {\n        ...baseSchema,\n        headline: data.title,\n        description: data.description,\n        author: {\n          '@type': 'Person',\n          name: data.author\n        },\n        datePublished: data.publishedAt,\n        dateModified: data.updatedAt,\n        image: data.image,\n        url: data.url\n      }\n    \n    default:\n      return baseSchema\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,qBAAqB,KAAa,EAAE,OAAe;IACjE,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW;IAC5C,MAAM,iBAAiB,mBAAmB;IAC1C,OAAO,CAAC,cAAc,EAAE,WAAW,MAAM,EAAE,gBAAgB;AAC7D;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAEO,SAAS,YAAY,SAAiB;IAC3C,4CAA4C;IAC5C,IAAI,UAAU,UAAU,CAAC,aAAa;QACpC,OAAO,CAAC,2FAA2F,CAAC;IACtG;IACA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,EAAE;IACpE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAEO,SAAS,aAAa,OAAgB;IAC3C,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ;IAClC,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9B;AAEO,SAAS,kBAAkB,IAAkD,EAAE,IAAS;IAC7F,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,KAAK,KAAK,GAAG;gBACb,WAAW,KAAK,KAAK;gBACrB,SAAS;oBACP,SAAS;oBACT,eAAe,KAAK,OAAO,CAAC,MAAM;oBAClC,iBAAiB,KAAK,OAAO,CAAC,IAAI;oBAClC,eAAe,KAAK,OAAO,CAAC,KAAK;oBACjC,gBAAgB,KAAK,OAAO,CAAC,OAAO;oBACpC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAClC;gBACA,KAAK,KAAK,GAAG;gBACb,cAAc,KAAK,YAAY;gBAC/B,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,QAAQ;oBACN,SAAS;oBACT,MAAM,KAAK,MAAM;gBACnB;gBACA,eAAe,KAAK,WAAW;gBAC/B,cAAc,KAAK,SAAS;gBAC5B,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG;YACf;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/lib/data.ts"], "sourcesContent": ["import servicesData from '@/data/services.json'\nimport packagesData from '@/data/packages.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport galleryData from '@/data/gallery.json'\nimport blogData from '@/data/blog.json'\nimport siteConfigData from '@/data/site-config.json'\nimport { formatPrice, formatDate, generateWhatsAppLink } from '@/lib/utils'\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  duration: string\n  price: string\n  image: string\n  category: string\n  popular: boolean\n}\n\nexport interface Package {\n  id: string\n  title: string\n  description: string\n  services: string[]\n  duration: string\n  originalPrice: string\n  discountedPrice: string\n  savings: string\n  image: string\n  popular: boolean\n  badge: string | null\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  location: string\n  service: string\n  rating: number\n  text: string\n  image: string\n  date: string\n  featured: boolean\n}\n\nexport interface GalleryItem {\n  id: string\n  title: string\n  description: string\n  image: string\n  category: string\n  featured: boolean\n  tags: string[]\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  author: string\n  publishedAt: string\n  updatedAt: string\n  featured: boolean\n  image: string\n  tags: string[]\n  category: string\n  readTime: string\n  seo: {\n    metaTitle: string\n    metaDescription: string\n    keywords: string[]\n  }\n}\n\nexport interface SiteConfig {\n  site: {\n    name: string\n    tagline: string\n    description: string\n    url: string\n    logo: string\n    favicon: string\n  }\n  contact: {\n    phone: string\n    whatsapp: string\n    email: string\n    address: {\n      street: string\n      city: string\n      state: string\n      country: string\n      zipCode: string\n    }\n    workingHours: Record<string, string>\n  }\n  social: {\n    facebook: string\n    instagram: string\n    tiktok: string\n    youtube: string\n  }\n  serviceAreas: Array<{\n    name: string\n    primary: boolean\n    travelFee: number\n  }>\n  whatsappMessage: string\n  seo: {\n    defaultTitle: string\n    defaultDescription: string\n    keywords: string[]\n    author: string\n    twitterHandle: string\n  }\n  analytics: {\n    googleAnalyticsId: string\n  }\n}\n\n// Services\nexport function getServices(): Service[] {\n  return servicesData.services\n}\n\nexport function getService(id: string): Service | undefined {\n  return servicesData.services.find(service => service.id === id)\n}\n\nexport function getPopularServices(): Service[] {\n  return servicesData.services.filter(service => service.popular)\n}\n\nexport function getServicesByCategory(category: string): Service[] {\n  return servicesData.services.filter(service => service.category === category)\n}\n\n// Packages\nexport function getPackages(): Package[] {\n  return packagesData.packages\n}\n\nexport function getPackage(id: string): Package | undefined {\n  return packagesData.packages.find(pkg => pkg.id === id)\n}\n\nexport function getPopularPackages(): Package[] {\n  return packagesData.packages.filter(pkg => pkg.popular)\n}\n\n// Testimonials\nexport function getTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials\n}\n\nexport function getFeaturedTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => testimonial.featured)\n}\n\nexport function getTestimonialsByService(service: string): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => \n    testimonial.service.toLowerCase().includes(service.toLowerCase())\n  )\n}\n\n// Gallery\nexport function getGalleryItems(): GalleryItem[] {\n  return galleryData.gallery\n}\n\nexport function getFeaturedGalleryItems(): GalleryItem[] {\n  return galleryData.gallery.filter(item => item.featured)\n}\n\nexport function getGalleryItemsByCategory(category: string): GalleryItem[] {\n  if (category === 'all') return galleryData.gallery\n  return galleryData.gallery.filter(item => item.category === category)\n}\n\nexport function getGalleryCategories() {\n  return galleryData.categories\n}\n\n// Blog\nexport function getBlogPosts(): BlogPost[] {\n  return blogData.posts\n}\n\nexport function getBlogPost(slug: string): BlogPost | undefined {\n  return blogData.posts.find(post => post.slug === slug)\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return blogData.posts.filter(post => post.featured)\n}\n\nexport function getBlogPostsByCategory(category: string): BlogPost[] {\n  return blogData.posts.filter(post => \n    post.category.toLowerCase().replace(/\\s+/g, '-') === category.toLowerCase()\n  )\n}\n\nexport function getBlogCategories() {\n  return blogData.categories\n}\n\nexport function getRelatedBlogPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {\n  return blogData.posts\n    .filter(post => \n      post.id !== currentPost.id && \n      (post.category === currentPost.category || \n       post.tags.some(tag => currentPost.tags.includes(tag)))\n    )\n    .slice(0, limit)\n}\n\n// Site Configuration\nexport function getSiteConfig(): SiteConfig {\n  return siteConfigData as SiteConfig\n}\n\nexport function getContactInfo() {\n  return siteConfigData.contact\n}\n\nexport function getSocialLinks() {\n  return siteConfigData.social\n}\n\nexport function getServiceAreas() {\n  return siteConfigData.serviceAreas\n}\n\nexport function getWhatsAppMessage() {\n  return siteConfigData.whatsappMessage\n}\n\nexport function getSEODefaults() {\n  return siteConfigData.seo\n}\n\n// Re-export utility functions\nexport { formatPrice, formatDate, generateWhatsAppLink }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAsHO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO;AAChE;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACtE;AAGO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AACtD;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO;AACxD;AAGO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY;AACtC;AAEO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ;AACjF;AAEO,SAAS,yBAAyB,OAAe;IACtD,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAC1C,YAAY,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;AAElE;AAGO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;AAC5B;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACzD;AAEO,SAAS,0BAA0B,QAAgB;IACxD,IAAI,aAAa,OAAO,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;IAClD,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,UAAU;AAC/B;AAGO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK;AACvB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AACnD;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACpD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAC3B,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,SAAS,SAAS,WAAW;AAE7E;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,UAAU;AAC5B;AAEO,SAAS,oBAAoB,WAAqB,EAAE,QAAgB,CAAC;IAC1E,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAClB,MAAM,CAAC,CAAA,OACN,KAAK,EAAE,KAAK,YAAY,EAAE,IAC1B,CAAC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,GAEvD,KAAK,CAAC,GAAG;AACd;AAGO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc;AACvB;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,OAAO;AAC/B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,MAAM;AAC9B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,YAAY;AACpC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,eAAe;AACvC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Facebook, Instagram, Phone, Mail, MapPin, Clock } from 'lucide-react'\nimport { getSiteConfig, getSocialLinks } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\n\nconst quickLinks = [\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Packages', href: '/packages' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n]\n\nconst services = [\n  { name: 'Bridal Makeup', href: '/services#bridal-makeup' },\n  { name: 'Party Makeup', href: '/services#party-makeup' },\n  { name: 'Engagement Makeup', href: '/services#engagement-makeup' },\n  { name: 'Traditional Makeup', href: '/services#traditional-makeup' },\n  { name: 'Photoshoot Makeup', href: '/services#photoshoot-makeup' },\n  { name: 'Makeup Lessons', href: '/services#makeup-lessons' },\n]\n\nexport default function Footer() {\n  const siteConfig = getSiteConfig()\n  const socialLinks = getSocialLinks()\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  return (\n    <footer className=\"bg-gradient-to-br from-cream to-soft-gray\">\n      <div className=\"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center\">\n                <span className=\"text-white font-display font-bold text-lg\">A</span>\n              </div>\n              <span className=\"font-display text-xl font-semibold text-text-primary\">\n                {siteConfig.site.name}\n              </span>\n            </div>\n            <p className=\"text-text-secondary text-sm leading-relaxed\">\n              {siteConfig.site.description}\n            </p>\n            <div className=\"flex space-x-4\">\n              <Link\n                href={socialLinks.facebook}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                aria-label=\"Facebook\"\n              >\n                <Facebook className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.instagram}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                aria-label=\"Instagram\"\n              >\n                <Instagram className=\"h-5 w-5\" />\n              </Link>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Services\n            </h3>\n            <ul className=\"space-y-2\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <Link\n                    href={service.href}\n                    className=\"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm\"\n                  >\n                    {service.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Contact Info\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3\">\n                <MapPin className=\"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0\" />\n                <div className=\"text-sm text-text-secondary\">\n                  <p>{siteConfig.contact.address.street}</p>\n                  <p>{siteConfig.contact.address.city}, {siteConfig.contact.address.state}</p>\n                  <p>{siteConfig.contact.address.country}</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Phone className=\"h-4 w-4 text-rose-gold-dark flex-shrink-0\" />\n                <Link\n                  href={whatsappLink}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                >\n                  {siteConfig.contact.phone}\n                </Link>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Mail className=\"h-4 w-4 text-rose-gold-dark flex-shrink-0\" />\n                <Link\n                  href={`mailto:${siteConfig.contact.email}`}\n                  className=\"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                >\n                  {siteConfig.contact.email}\n                </Link>\n              </div>\n              \n              <div className=\"flex items-start space-x-3\">\n                <Clock className=\"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0\" />\n                <div className=\"text-sm text-text-secondary\">\n                  <p>Mon-Sat: 9:00 AM - 6:00 PM</p>\n                  <p>Sun: 10:00 AM - 4:00 PM</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Service Areas */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"text-center\">\n            <h4 className=\"font-display text-lg font-semibold text-text-primary mb-3\">\n              Service Areas\n            </h4>\n            <p className=\"text-text-secondary text-sm\">\n              Serving clients across{' '}\n              {siteConfig.serviceAreas.map((area, index) => (\n                <span key={area.name}>\n                  {area.name}\n                  {index < siteConfig.serviceAreas.length - 1 && ', '}\n                </span>\n              ))}\n            </p>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"text-center\">\n            <p className=\"text-text-muted text-sm\">\n              © {new Date().getFullYear()} {siteConfig.site.name}. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,WAAW;IACf;QAAE,MAAM;QAAiB,MAAM;IAA0B;IACzD;QAAE,MAAM;QAAgB,MAAM;IAAyB;IACvD;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAsB,MAAM;IAA+B;IACnE;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAkB,MAAM;IAA2B;CAC5D;AAEc,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAK,WAAU;sDACb,WAAW,IAAI,CAAC,IAAI;;;;;;;;;;;;8CAGzB,8OAAC;oCAAE,WAAU;8CACV,WAAW,IAAI,CAAC,WAAW;;;;;;8CAE9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,QAAQ;4CAC1B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,SAAS;4CAC3B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;;;;;;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;sCAa3B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,MAAM;;;;;;sEACrC,8OAAC;;gEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,IAAI;gEAAC;gEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;sEACvE,8OAAC;sEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAET,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,WAAW,OAAO,CAAC,KAAK,EAAE;oDAC1C,WAAU;8DAET,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;;oCAA8B;oCAClB;oCACtB,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;;gDACE,KAAK,IAAI;gDACT,QAAQ,WAAW,YAAY,CAAC,MAAM,GAAG,KAAK;;2CAFtC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAU5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAA0B;gCAClC,IAAI,OAAO,WAAW;gCAAG;gCAAE,WAAW,IAAI,CAAC,IAAI;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Playfair_Display, Pop<PERSON><PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/header\";\nimport Footer from \"@/components/layout/footer\";\nimport JsonLd from \"@/components/seo/json-ld\";\nimport { getSiteConfig } from \"@/lib/data\";\n\nconst playfairDisplay = Playfair_Display({\n  variable: \"--font-display\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst poppins = Poppins({\n  variable: \"--font-sans\",\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\nconst siteConfig = getSiteConfig();\n\nexport const metadata: Metadata = {\n  title: siteConfig.seo.defaultTitle,\n  description: siteConfig.seo.defaultDescription,\n  keywords: siteConfig.seo.keywords,\n  authors: [{ name: siteConfig.seo.author }],\n  creator: siteConfig.seo.author,\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: siteConfig.site.url,\n    title: siteConfig.seo.defaultTitle,\n    description: siteConfig.seo.defaultDescription,\n    siteName: siteConfig.site.name,\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: siteConfig.seo.defaultTitle,\n    description: siteConfig.seo.defaultDescription,\n    creator: siteConfig.seo.twitterHandle,\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className={`${playfairDisplay.variable} ${poppins.variable}`}>\n      <body className=\"antialiased\">\n        <Header />\n        <main className=\"pt-16\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AAAA;;;;;;;;AAeA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,WAAW,GAAG,CAAC,YAAY;IAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;IAC9C,UAAU,WAAW,GAAG,CAAC,QAAQ;IACjC,SAAS;QAAC;YAAE,MAAM,WAAW,GAAG,CAAC,MAAM;QAAC;KAAE;IAC1C,SAAS,WAAW,GAAG,CAAC,MAAM;IAC9B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,WAAW,IAAI,CAAC,GAAG;QACxB,OAAO,WAAW,GAAG,CAAC,YAAY;QAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;QAC9C,UAAU,WAAW,IAAI,CAAC,IAAI;IAChC;IACA,SAAS;QACP,MAAM;QACN,OAAO,WAAW,GAAG,CAAC,YAAY;QAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;QAC9C,SAAS,WAAW,GAAG,CAAC,aAAa;IACvC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,GAAG,oJAAA,CAAA,UAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,EAAE;kBAC1E,cAAA,8OAAC;YAAK,WAAU;;8BACd,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}