'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, Clock, ArrowRight, Filter, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { getBlogPosts, getBlogCategories } from '@/lib/data'
import { formatDate } from '@/lib/utils'

export default function BlogGrid() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const blogPosts = getBlogPosts()
  const categories = getBlogCategories()

  const filteredPosts = selectedCategory === 'all' 
    ? blogPosts 
    : blogPosts.filter(post => 
        post.category.toLowerCase().replace(/\s+/g, '-') === selectedCategory
      )

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Latest Articles"
          title="Beauty Blog & Tips"
          description="Stay updated with the latest beauty trends, professional tips, and expert advice to enhance your makeup skills and knowledge."
        />
      </AnimatedElement>

      {/* Category Filter */}
      <AnimatedElement animation="slideUp" delay={0.3} className="mb-12">
        <div className="flex flex-wrap justify-center gap-3">
          <Button
            variant={selectedCategory === 'all' ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory('all')}
            className="group"
          >
            <Filter className="w-4 h-4 mr-2" />
            All Articles
            <Badge variant="secondary" className="ml-2 text-xs">
              {blogPosts.length}
            </Badge>
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="group"
            >
              <Filter className="w-4 h-4 mr-2" />
              {category.name}
              <Badge variant="secondary" className="ml-2 text-xs">
                {category.count}
              </Badge>
            </Button>
          ))}
        </div>
      </AnimatedElement>

      {/* Featured Post */}
      {selectedCategory === 'all' && (
        <AnimatedElement animation="slideUp" delay={0.4} className="mb-12">
          {blogPosts.filter(post => post.featured)[0] && (
            <Card className="overflow-hidden border-0 bg-gradient-to-br from-rose-gold/10 to-blush-pink/10">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative aspect-[16/9] lg:aspect-auto">
                  <Image
                    src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=400&fit=crop&crop=face&q=80"
                    alt={blogPosts.filter(post => post.featured)[0].title}
                    fill
                    className="object-cover"
                  />
                  <Badge className="absolute top-4 left-4" variant="default">
                    Featured
                  </Badge>
                </div>
                <div className="p-8 flex flex-col justify-center">
                  <Badge variant="secondary" className="w-fit mb-3">
                    {blogPosts.filter(post => post.featured)[0].category}
                  </Badge>
                  <h2 className="font-display text-2xl md:text-3xl font-bold text-text-primary mb-4">
                    {blogPosts.filter(post => post.featured)[0].title}
                  </h2>
                  <p className="text-text-secondary mb-6 leading-relaxed">
                    {blogPosts.filter(post => post.featured)[0].excerpt}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-text-muted mb-6">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      <span>{blogPosts.filter(post => post.featured)[0].author}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(blogPosts.filter(post => post.featured)[0].publishedAt)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      <span>{blogPosts.filter(post => post.featured)[0].readTime}</span>
                    </div>
                  </div>
                  <Button asChild variant="gradient" className="w-fit group">
                    <Link href={`/blog/${blogPosts.filter(post => post.featured)[0].slug}`}>
                      Read Full Article
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </AnimatedElement>
      )}

      {/* Blog Posts Grid */}
      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredPosts.filter(post => selectedCategory !== 'all' || !post.featured).map((post, index) => (
          <StaggeredItem key={post.id}>
            <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white overflow-hidden">
              {/* Post Image */}
              <div className="relative aspect-[16/9] overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=250&fit=crop&crop=face&q=80"
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <Badge variant="secondary" className="text-xs bg-white/90 text-text-primary">
                    {post.category}
                  </Badge>
                </div>

                {/* Featured Badge */}
                {post.featured && (
                  <div className="absolute top-4 right-4">
                    <Badge variant="default" className="text-xs">
                      Featured
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-3">
                <CardTitle className="text-xl group-hover:text-rose-gold-dark transition-colors line-clamp-2">
                  {post.title}
                </CardTitle>
                <CardDescription className="text-text-secondary line-clamp-3">
                  {post.excerpt}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4 flex-1 flex flex-col">
                {/* Post Meta */}
                <div className="flex items-center gap-4 text-sm text-text-muted">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-sm text-text-muted">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.publishedAt)}</span>
                </div>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {post.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                {/* Read More Button */}
                <div className="pt-4 mt-auto">
                  <Button 
                    asChild 
                    variant="outline" 
                    className="w-full group"
                    size="sm"
                  >
                    <Link href={`/blog/${post.slug}`}>
                      Read Article
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* No Posts Message */}
      {filteredPosts.length === 0 && (
        <AnimatedElement animation="slideUp" delay={0.6} className="text-center py-12">
          <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-4">
            <BookOpen className="w-8 h-8 text-white" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            No Articles Found
          </h3>
          <p className="text-text-secondary mb-6">
            No articles found in this category. Try selecting a different category or check back later for new content.
          </p>
          <Button 
            variant="outline" 
            onClick={() => setSelectedCategory('all')}
          >
            View All Articles
          </Button>
        </AnimatedElement>
      )}

      {/* Load More Button */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-12 text-center">
        <p className="text-text-secondary mb-6">
          Showing {filteredPosts.length} articles
        </p>
        <Button variant="outline" size="lg" disabled>
          <BookOpen className="w-5 h-5 mr-2" />
          More Articles Coming Soon
        </Button>
      </AnimatedElement>
    </Section>
  )
}
