import { Metadata } from 'next'
import PackagesHero from '@/components/sections/packages-hero'
import PackagesGrid from '@/components/sections/packages-grid'
import PackagesCTA from '@/components/sections/packages-cta'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `Makeup Packages & Deals | ${siteConfig.site.name}`,
  description: 'Save more with our curated makeup packages. Complete bridal packages, party packages, and special deals for multiple services in Nepal.',
  openGraph: {
    title: `Makeup Packages & Deals | ${siteConfig.site.name}`,
    description: 'Save more with our curated makeup packages. Complete bridal packages, party packages, and special deals for multiple services in Nepal.',
    url: `${siteConfig.site.url}/packages`,
  },
}

export default function PackagesPage() {
  return (
    <>
      <PackagesHero />
      <PackagesGrid />
      <PackagesCTA />
    </>
  )
}
