import { Metadata } from 'next'
import ContactHero from '@/components/sections/contact-hero'
import ContactInfo from '@/components/sections/contact-info'
import ContactFormSection from '@/components/sections/contact-form-section'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `Contact Us | ${siteConfig.site.name}`,
  description: 'Get in touch with our professional makeup artist. Book consultations, ask questions, or schedule your makeup session in Biratnagar and surrounding areas.',
  openGraph: {
    title: `Contact Us | ${siteConfig.site.name}`,
    description: 'Get in touch with our professional makeup artist. Book consultations, ask questions, or schedule your makeup session in Biratnagar and surrounding areas.',
    url: `${siteConfig.site.url}/contact`,
  },
}

export default function ContactPage() {
  return (
    <>
      <ContactHero />
      <ContactInfo />
      <ContactFormSection />
    </>
  )
}
