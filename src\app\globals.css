@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #fefefe;
  --foreground: #2d2d2d;

  /* Pastel Theme Colors */
  --rose-gold: #e8b4b8;
  --rose-gold-light: #f2d1d4;
  --rose-gold-dark: #d49ca1;

  --blush-pink: #f4c2c2;
  --blush-pink-light: #f8d7d7;
  --blush-pink-dark: #e8a8a8;

  --lavender: #e6e6fa;
  --lavender-light: #f0f0fc;
  --lavender-dark: #d1d1f0;

  --cream: #faf7f2;
  --soft-gray: #f8f8f8;
  --text-primary: #2d2d2d;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Custom Colors */
  --color-rose-gold: var(--rose-gold);
  --color-rose-gold-light: var(--rose-gold-light);
  --color-rose-gold-dark: var(--rose-gold-dark);

  --color-blush-pink: var(--blush-pink);
  --color-blush-pink-light: var(--blush-pink-light);
  --color-blush-pink-dark: var(--blush-pink-dark);

  --color-lavender: var(--lavender);
  --color-lavender-light: var(--lavender-light);
  --color-lavender-dark: var(--lavender-dark);

  --color-cream: var(--cream);
  --color-soft-gray: var(--soft-gray);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);

  /* Typography */
  --font-display: 'Playfair Display', serif;
  --font-sans: 'Poppins', system-ui, sans-serif;

  /* Spacing and Sizing */
  --container-max-width: 1200px;
  --section-padding: 5rem;
  --section-padding-mobile: 3rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --foreground: #f5f5f5;
    --text-primary: #f5f5f5;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--soft-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--rose-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--rose-gold-dark);
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
