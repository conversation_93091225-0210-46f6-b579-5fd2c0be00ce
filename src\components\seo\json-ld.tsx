import { getSiteConfig } from '@/lib/data'

interface JsonLdProps {
  type: 'Organization' | 'LocalBusiness' | 'Article' | 'WebSite'
  data?: any
}

export default function JsonLd({ type, data }: JsonLdProps) {
  const siteConfig = getSiteConfig()

  const generateSchema = () => {
    const baseSchema = {
      '@context': 'https://schema.org',
      '@type': type
    }

    switch (type) {
      case 'Organization':
        return {
          ...baseSchema,
          name: siteConfig.site.name,
          description: siteConfig.site.description,
          url: siteConfig.site.url,
          logo: `${siteConfig.site.url}/images/logo.png`,
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: siteConfig.contact.phone,
            contactType: 'customer service',
            availableLanguage: ['English', 'Nepali']
          },
          address: {
            '@type': 'PostalAddress',
            streetAddress: siteConfig.contact.address.street,
            addressLocality: siteConfig.contact.address.city,
            addressRegion: siteConfig.contact.address.state,
            addressCountry: siteConfig.contact.address.country,
            postalCode: siteConfig.contact.address.zipCode
          },
          sameAs: [
            siteConfig.social.facebook,
            siteConfig.social.instagram,
            siteConfig.social.tiktok
          ]
        }

      case 'LocalBusiness':
        return {
          ...baseSchema,
          '@type': 'BeautySalon',
          name: siteConfig.site.name,
          description: siteConfig.site.description,
          url: siteConfig.site.url,
          telephone: siteConfig.contact.phone,
          email: siteConfig.contact.email,
          address: {
            '@type': 'PostalAddress',
            streetAddress: siteConfig.contact.address.street,
            addressLocality: siteConfig.contact.address.city,
            addressRegion: siteConfig.contact.address.state,
            addressCountry: siteConfig.contact.address.country,
            postalCode: siteConfig.contact.address.zipCode
          },
          geo: {
            '@type': 'GeoCoordinates',
            latitude: '26.4525', // Biratnagar coordinates
            longitude: '87.2718'
          },
          openingHours: [
            'Mo-Sa 09:00-18:00',
            'Su 10:00-16:00'
          ],
          priceRange: 'NPR 4,000 - NPR 25,000',
          serviceArea: siteConfig.serviceAreas.map(area => ({
            '@type': 'City',
            name: area.name
          })),
          hasOfferCatalog: {
            '@type': 'OfferCatalog',
            name: 'Makeup Services',
            itemListElement: [
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Bridal Makeup',
                  description: 'Complete bridal transformation with traditional and modern looks'
                }
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Party Makeup',
                  description: 'Glamorous looks for special occasions and celebrations'
                }
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Traditional Makeup',
                  description: 'Authentic traditional looks for cultural ceremonies'
                }
              }
            ]
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '5.0',
            reviewCount: '50',
            bestRating: '5',
            worstRating: '1'
          },
          sameAs: [
            siteConfig.social.facebook,
            siteConfig.social.instagram,
            siteConfig.social.tiktok
          ]
        }

      case 'WebSite':
        return {
          ...baseSchema,
          name: siteConfig.site.name,
          description: siteConfig.site.description,
          url: siteConfig.site.url,
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${siteConfig.site.url}/search?q={search_term_string}`
            },
            'query-input': 'required name=search_term_string'
          },
          publisher: {
            '@type': 'Organization',
            name: siteConfig.site.name,
            logo: {
              '@type': 'ImageObject',
              url: `${siteConfig.site.url}/images/logo.png`
            }
          }
        }

      case 'Article':
        if (!data) return baseSchema
        return {
          ...baseSchema,
          headline: data.title,
          description: data.excerpt,
          author: {
            '@type': 'Person',
            name: data.author
          },
          publisher: {
            '@type': 'Organization',
            name: siteConfig.site.name,
            logo: {
              '@type': 'ImageObject',
              url: `${siteConfig.site.url}/images/logo.png`
            }
          },
          datePublished: data.publishedAt,
          dateModified: data.updatedAt,
          image: data.image ? `${siteConfig.site.url}${data.image}` : `${siteConfig.site.url}/images/blog-default.jpg`,
          url: `${siteConfig.site.url}/blog/${data.slug}`,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${siteConfig.site.url}/blog/${data.slug}`
          },
          keywords: data.tags?.join(', '),
          articleSection: data.category,
          wordCount: data.wordCount || 1000
        }

      default:
        return baseSchema
    }
  }

  const schema = generateSchema()

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}
