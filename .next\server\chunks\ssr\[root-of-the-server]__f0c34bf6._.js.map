{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali/src/app/portfolio/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport PortfolioHero from '@/components/sections/portfolio-hero'\nimport PortfolioGallery from '@/components/sections/portfolio-gallery'\nimport PortfolioCTA from '@/components/sections/portfolio-cta'\nimport { getSiteConfig } from '@/lib/data'\n\nconst siteConfig = getSiteConfig()\n\nexport const metadata: Metadata = {\n  title: `Portfolio & Gallery | ${siteConfig.site.name}`,\n  description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',\n  openGraph: {\n    title: `Portfolio & Gallery | ${siteConfig.site.name}`,\n    description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',\n    url: `${siteConfig.site.url}/portfolio`,\n  },\n}\n\nexport default function PortfolioPage() {\n  return (\n    <>\n      <PortfolioHero />\n      <PortfolioGallery />\n      <PortfolioCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA;AAAA;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,CAAC,sBAAsB,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;IACtD,aAAa;IACb,WAAW;QACT,OAAO,CAAC,sBAAsB,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE;QACtD,aAAa;QACb,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;IACzC;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;;;AAGP", "debugId": null}}]}